Leave Management API (Mini HR Tool) 
Type: Backend API 
Estimated Time: 2–2.5 hours 
Target Roles: SE II / SSE 
 
Instructions 
• • Build a RESTful backend API to manage employee leave requests. 
• • Implement CRUD endpoints for Leave (POST, GET, PATCH). 
• • Leave object should include: employee_id, start_date, end_date, status (default: 'pending'). 
• • Support filtering leaves by employee_id and status. 
• • Bonus: Prevent overlapping leave requests. 
 
Evaluation Criteria 
- Functional Completeness 
- Setup 
- Error Handling 
- Adherence to Protocols 
- Code Quality 
- Tests